import firebase from 'firebase/app';
import 'firebase/remote-config';

export const paSuitableStatuses = ['partially suitable', 'suitable'];

export const processingStatuses = {
  'partially suitable': 'partially_suitable',
  'not suitable': 'not_suitable',
  suitable: 'suitable',
  failed: 'failed',
};

export const NdviTypes = ['default', 'contrasted'];

// It is important to keep number of items the same in these arrays
// This is required to allow automatically converting them to different unit system

export const FieldFillingTypes = [...NdviTypes];
const remoteConfig = firebase.remoteConfig();

remoteConfig.fetchAndActivate().then(() => {
  const isRgbLayerActive = remoteConfig.getValue('rgb_layer').asBoolean();
  if (isRgbLayerActive) {
    FieldFillingTypes.push('rgb');
  }
});

export const FieldListSettingsKeys = {
  orderBy: 'orderBy',
  sortBy: 'sortBy',
  groupBy: 'groupBy',
};

export const FieldListSortOptions = [
  {
    value: 'name',
    type: 'alphabetical',
  },
  {
    value: 'vegetation_index',
    type: 'number',
  },
  {
    value: 'yield_value',
    type: 'yield_number',
  },
  {
    value: 'sowing_date',
    type: 'date',
  },
  {
    value: 'harvest_date',
    type: 'date',
  },
  {
    value: 'crop',
    type: 'alphabetical',
  },
];
