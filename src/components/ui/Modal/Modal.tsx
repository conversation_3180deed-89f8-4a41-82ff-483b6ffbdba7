import React, {
  type FC,
  type ReactNode,
  Children,
  isValidElement,
  useRef,
  useEffect,
} from 'react';
import { useTransition, useSpring } from 'react-spring';
import { cx } from 'linaria';

import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import ModalContent, {
  ModalContentProps,
} from 'components/ui/Modal/ModalContent/ModalContent';
import RenderAboveEverything from 'components/RenderAboveEverything';

import {
  StyledModal,
  StyledOverlay,
  styles,
} from 'components/ui/Modal/Modal.style';

type ModalProps = {
  ModalActions?: ModalContentProps['ModalActions'];
  ModalBody?: ModalContentProps['ModalBody'];
  ModalHeader?: ModalContentProps['ModalHeader'];
  children?: TEMPORARY_ANY;
  designMode?: 'default' | 'new';
  maxHeight?: number;
  onClose?: () => void;
  onDestroyed?: () => void;
  show: boolean;
  width?: Nullable<number>;
  withCloseButton?: boolean;
  withContentPadding?: boolean;
};

type ModalElementProps = {
  children?: ReactNode;
  designMode?: 'default' | 'new';
};

type ModalType = FC<ModalProps> & {
  Actions: FC<ModalElementProps>;
  Body: FC<ModalElementProps>;
  Head: FC<ModalElementProps>;
};

const Modal: ModalType = ({
  ModalActions,
  ModalBody,
  ModalHeader,
  children,
  designMode = 'default',
  maxHeight,
  onClose,
  onDestroyed,
  show,
  width,
  withCloseButton = false,
  withContentPadding = false,
}) => {
  const backdrop = useRef<HTMLDivElement>(null);
  const mouseDownTarget = useRef<EventTarget | null>(null);

  const childrenWithDesignMode = Children.map(children, child => {
    if (
      isValidElement<ModalElementProps>(child) &&
      (child.type === Modal.Head ||
        child.type === Modal.Body ||
        child.type === Modal.Actions)
    ) {
      return React.cloneElement(child, {
        designMode,
      } as Partial<ModalElementProps>);
    }

    return child;
  });

  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const transitions = useTransition(show, null, {
    from: { visibility: 'hidden', opacity: 0 },
    enter: { visibility: 'visible', opacity: 1 },
    leave: { visibility: 'visible', opacity: 0 },
    config: { duration: 100 },
    onDestroyed: isDestroyed => {
      if (isDestroyed && isMountedRef.current) {
        onDestroyed?.();
      }
    },
  });
  const scaleProps = useSpring({
    from: { transform: show ? 'scale(0.8)' : 'scale(1)' },
    to: { transform: show ? 'scale(1)' : 'scale(0.8)' },
    config: { duration: 100 },
  });
  return (
    <RenderAboveEverything>
      {transitions.map(
        ({ item, key, props }) =>
          item && (
            <StyledOverlay
              key={key}
              ref={backdrop}
              style={props}
              onMouseDown={e => {
                mouseDownTarget.current = e.target;
              }}
              onMouseUp={e => {
                if (
                  e.target === mouseDownTarget.current &&
                  e.target === backdrop.current
                ) {
                  // somehow onClick triggers even if onMouseDown target and onMouseUp not equal
                  // i fixed it by manually compare targets with backdrop...
                  onClose?.();
                }
              }}
            >
              <StyledModal
                width={width}
                withContentPadding={withContentPadding}
                style={scaleProps}
              >
                <ModalContent
                  children={childrenWithDesignMode}
                  ModalActions={ModalActions}
                  ModalHeader={ModalHeader}
                  ModalBody={ModalBody}
                  maxHeight={maxHeight}
                  onClick={e => e.stopPropagation()}
                />
                {withCloseButton && (
                  <Button
                    className={styles.closeButton}
                    onClick={e => {
                      e.stopPropagation();
                      onClose?.();
                    }}
                  >
                    <Icon name='close' />
                  </Button>
                )}
              </StyledModal>
            </StyledOverlay>
          ),
      )}
    </RenderAboveEverything>
  );
};

Modal.Head = ({ children, designMode = 'default' }) => (
  <div className={cx(styles.modalHeader, styles[`${designMode}ModalHeader`])}>
    {children}
  </div>
);

Modal.Body = ({ children, designMode = 'default' }) => (
  <div className={cx(styles.modalBody, styles[`${designMode}ModalBody`])}>
    {children}
  </div>
);

Modal.Actions = ({ children, designMode = 'default' }) => (
  <div className={cx(styles.modalActions, styles[`${designMode}ModalActions`])}>
    {children}
  </div>
);

export default Modal;
