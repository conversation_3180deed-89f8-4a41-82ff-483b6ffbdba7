import React, { VFC } from 'react';
import { css } from 'linaria';

import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';

import { useTranslate } from 'utils/use-translate';

const popupText = css`
  font-size: 16px;
  line-height: 22px;
  text-align: center;
`;

type ErrorPopupProps = {
  title: React.ReactNode;
  description: React.ReactNode[];
  onClose: () => void;
};

const ErrorPopup: VFC<ErrorPopupProps> = ({ title, description, onClose }) => {
  const { t } = useTranslate();

  return (
    <Modal
      show
      width={400}
      onClose={onClose}
      ModalHeader={() => <div style={{ textAlign: 'center' }}>{title}</div>}
      ModalBody={() => (
        <div className={popupText}>
          {description.map((line, i) => (
            <div key={i}>{line}</div>
          ))}
        </div>
      )}
      ModalActions={() => (
        <>
          <Button className='btn btn-danger btn-lg' onClick={onClose}>
            {t('forms.close')}
          </Button>
        </>
      )}
    />
  );
};

export default ErrorPopup;
