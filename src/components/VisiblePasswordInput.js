import React, { useState } from 'react';
import { css } from 'linaria';

import Button from './ui/Button/Button';
import TextInput from './forms/TextInput';
import Icon from './Icon';

const eyeButtonStyle = css`
  padding: 4px 12px 4px 4px;
  margin: 0;
  border: none;
  background: transparent;
  color: #a5b2bc;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const VisiblePasswordInput = React.forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);

  return (
    <TextInput
      type={visible ? 'text' : 'password'}
      ref={ref}
      suffix={
        <Button
          className={eyeButtonStyle}
          onClick={() => {
            setVisible(!visible);
          }}
        >
          {!visible && <Icon name={'eye-opened'} />}
          {visible && <Icon name={'eye-closed'} />}
        </Button>
      }
      {...props}
    />
  );
});

export default VisiblePasswordInput;
