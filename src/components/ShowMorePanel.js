import React, { useState } from 'react';
import Button from './ui/Button/Button';
import noop from 'lodash/noop';
import { cx } from 'linaria';
import { styles } from './ShowMorePanel.styles';

const ShowMorePanel = ({ children, disabled, triggerLabel }) => {
  const [expanded, setExpanded] = useState(false);
  return (
    <div className={cx(styles.showMoreContainer, expanded && '__opened')}>
      <Button
        className={styles.showMoreTrigger}
        onClick={
          disabled
            ? noop
            : () => {
                setExpanded(!expanded);
              }
        }
      >
        <span className={cx(styles.showMoreText)}>{triggerLabel}</span>
      </Button>
      <div className={styles.showMorePanel}>{children}</div>
    </div>
  );
};

export default ShowMorePanel;
