<?xml version="1.0" encoding="UTF-8"?>
<svg width="60px" height="52px" viewBox="0 0 60 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 50.2 (55047) - http://www.bohemiancoding.com/sketch -->
    <title>ic_hail_60</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="15.1792921%" y1="95.9611558%" x2="75.2407627%" y2="27.8873478%" id="linearGradient-1">
            <stop stop-color="#509CD0" offset="0%"></stop>
            <stop stop-color="#61A6E5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Актуальное" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icons" transform="translate(-164.000000, -346.000000)">
            <g id="ic_weather_60" transform="translate(53.000000, 29.000000)">
                <g id="ic_hail_60" transform="translate(111.000000, 317.000000)">
                    <rect id="Rectangle-8" fill="#D8D8D8" opacity="0" x="0" y="0" width="60" height="52"></rect>
                    <g id="Group-47" transform="translate(2.000000, 4.000000)">
                        <path d="M27.99609,0.9856 C22.34649,0.9856 17.73679,5.00588 16.55119,10.31107 C14.48479,11.1257 12.94819,12.6358 12.04749,14.6559 C11.70969,14.6161 11.39839,14.5499 11.04069,14.5499 C5.44412,14.5499 0.86749,19.1265 0.86749,24.7232 C0.86749,30.3198 5.44412,34.8964 11.04069,34.8964 L44.95149,34.8964 C50.54809,34.8964 55.12479,30.3198 55.12479,24.7232 C55.12479,21.0539 52.97219,18.0337 50.03809,16.2455 C50.03809,10.64885 45.46149,6.07222 39.86489,6.07222 C39.12969,6.07222 38.51379,6.29078 37.85149,6.44312 C35.73199,3.19113 32.15549,0.9856 27.99609,0.9856 Z" id="Shape" fill="url(#linearGradient-1)" fill-rule="nonzero"></path>
                        <circle id="Oval-3" fill="#5BC0DD" cx="17" cy="43" r="3"></circle>
                        <circle id="Oval-3" fill="#5BC0DD" cx="41" cy="43" r="3"></circle>
                        <circle id="Oval-3" fill="#5BC0DD" cx="29" cy="40" r="3"></circle>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>