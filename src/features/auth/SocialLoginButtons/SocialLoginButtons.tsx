import React from 'react';

// import startCase from 'lodash/startCase';
import { GoogleOAuthProvider } from '@react-oauth/google';

// @ts-ignore
import config from 'config/index';
// @ts-ignore
// import { FacebookMock } from 'sagas/global/integrations';

// import { useTranslate } from 'utils/use-translate';
// import { parseQuery } from 'utils/query';

// import SocialLoginButton from './SocialLoginButton';
import { GoogleLoginButton } from 'features/auth/GoogleLoginButton';
import { OnSocialLoginCallback } from './types';

// import logEvent from 'sagas/global/logEvent';

const PROVIDER_LIST = ['google'] as const;

interface Props {
  location: TEMPORARY_ANY;
  partnerType: string | undefined;
  pendingProvider: string;
  type: string;
  onSocialLogin: OnSocialLoginCallback;
}

const SocialLoginButtons = ({
  location,
  // partnerType,
  // pendingProvider,
  type,
  onSocialLogin,
}: Props) => {
  // const { t } = useTranslate();

  return PROVIDER_LIST.map(provider => (
    <div className='onesoil-hello-socials__item size-full' key={provider}>
      {/* @ts-ignore: react-social-login typings are broken */}

      {
        provider === 'google' ? (
          <GoogleOAuthProvider clientId={config.appIDs.google}>
            <GoogleLoginButton
              type={type}
              location={location}
              onSocialLogin={onSocialLogin}
            />
          </GoogleOAuthProvider>
        ) : null
        // (
        //   <SocialLoginButton
        //     appId={config.appIDs[provider]}
        //     provider={provider}
        //     network={provider}
        //     pending={pendingProvider === provider}
        //     onLoginSuccess={user => {
        //       const query = parseQuery(location);
        //       const token = user.token.idToken || user.token.accessToken;

        //       onSocialLogin(provider, { token }, partnerType, query.d);

        //       if (
        //         partnerType &&
        //         ['accept-workspace-invite', 'accept-client-invite'].includes(
        //           partnerType,
        //         )
        //       ) {
        //         logEvent('workspace_open_invitation_web', {
        //           platform: 'web',
        //         });
        //       } else if (partnerType === 'accept-consultant-invite') {
        //         logEvent('consultant_open_invitation', {
        //           platform: 'web',
        //         });
        //       }
        //       // @ts-ignore
        //       const { fbq = FacebookMock } = window;
        //       fbq('trackCustom', startCase(provider));
        //     }}
        //     onLoginFailure={({ message }) => {
        //       // it's a known issue https://developers.google.com/identity/sign-in/web/troubleshooting#third-party_cookies_and_data_blocked

        //       // react-social-login has third-party that's blocked in Chrome
        //       // we only can catch actually this case & not show error for it 🤷‍♀️
        //       const isThirdPartyDisabled =
        //         message
        //           .replace(/\s/g, '')
        //           .indexOf('Cookiesarenotenabledincurrentenvironment.') > -1;

        //       // Message error details:
        //       // "Not a valid origin for the client: <DOMAIN> has not been registered for client ID <APP_ID>.
        //       // Please go to https://console.developers.google.com/ and register this origin for your project's client ID."
        //       const SDKNotLoaded =
        //         message.indexOf('[google][load] Failed to load SDK') > -1;

        //       if (!isThirdPartyDisabled && !SDKNotLoaded) {
        //         alert(t('auth.social.error'));
        //         // TODO: Handle all vendor-specific exceptions and show appropriate messages
        //         // We have already captured tons of errors
        //         // captureException(error);
        //       }
        //     }}
        //   />
        // )
      }
    </div>
  ));
};

export default SocialLoginButtons;
