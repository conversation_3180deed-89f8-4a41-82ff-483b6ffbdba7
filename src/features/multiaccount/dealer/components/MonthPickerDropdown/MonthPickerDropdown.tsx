import React, { useState, useEffect, useRef } from 'react';

import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
} from 'components/ui/Popover';

import { MonthPicker } from 'features/multiaccount/dealer/components/MonthPickerDropdown/MonthPicker';

import { useTranslate } from 'utils/use-translate';

import { styles } from './MonthPickerDropdown.styles';

type MonthPickerDropdownProps = {
  className?: string;
  disabled?: boolean;
  getSelectedLabel?: (value: string) => string;
  maxDate?: string; // YYYY-MM format
  minDate?: string; // YYYY-MM format
  onChange: (monthYear: string) => void;
  placeholder?: string;
  value: string; // YYYY-MM format
};

function MonthPickerDropdown({
  className,
  disabled = false,
  getSelectedLabel,
  maxDate,
  minDate,
  onChange,
  placeholder = 'Select Month',
  value,
}: MonthPickerDropdownProps) {
  const { t } = useTranslate();

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  const handleCloseDropdown = () => {
    setIsDropdownOpen(false);
  };

  const handleMonthChange = (monthYear: string) => {
    onChange(monthYear);
    handleCloseDropdown();
  };

  const handleClickOutside = (event: MouseEvent): void => {
    const target = event.target as Element;
    // Check if click is outside container AND not inside any popover content
    if (
      containerRef.current &&
      !containerRef.current.contains(target) &&
      !target.closest('.month-picker-dropdown-popover')
    ) {
      setIsDropdownOpen(false);
    }
  };

  // NOTE: Handle click outside to close dropdown
  useEffect(() => {
    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isDropdownOpen]);

  const getDisplayLabel = () => {
    if (getSelectedLabel && value) {
      return getSelectedLabel(value);
    }

    if (value) {
      // NOTE: Default format: convert "2024-01" to "January 2024"
      const parts = value.split('-');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const year = parts[0];
        const month = parts[1];
        const monthNames = [
          t('months.january'),
          t('months.february'),
          t('months.march'),
          t('months.april'),
          t('months.may'),
          t('months.june'),
          t('months.july'),
          t('months.august'),
          t('months.september'),
          t('months.october'),
          t('months.november'),
          t('months.december'),
        ];
        const monthIndex = parseInt(month, 10) - 1;
        if (monthIndex >= 0 && monthIndex < 12) {
          return `${monthNames[monthIndex]} ${year}`;
        }
      }
    }

    return placeholder;
  };

  return (
    <div ref={containerRef} className={styles.monthPickerDropdownContainer}>
      <PopoverDeprecated
        active={isDropdownOpen}
        align={PopoverDeprecatedAlign.BottomLeft}
        offset={[0, 8]}
        renderTrigger={props => (
          <button
            {...props}
            type='button'
            className={`
              ${styles.monthPickerDropdownButton}
              ${className || ''}
              ${disabled ? styles.monthPickerDropdownButtonDisabled : ''}
              ${isDropdownOpen ? 'form-select--active' : ''}
            `}
            onClick={handleToggleDropdown}
            disabled={disabled}
          >
            <span className={styles.monthPickerDropdownLabel}>
              {getDisplayLabel()}
            </span>
          </button>
        )}
        renderPopover={props => (
          <div {...props} className='month-picker-dropdown-popover'>
            <MonthPicker
              value={value}
              minDate={minDate}
              maxDate={maxDate}
              onChange={handleMonthChange}
              onClose={handleCloseDropdown}
            />
          </div>
        )}
      />
    </div>
  );
}

export default MonthPickerDropdown;
