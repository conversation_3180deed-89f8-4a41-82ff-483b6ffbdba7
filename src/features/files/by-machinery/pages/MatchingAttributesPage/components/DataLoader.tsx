import React, { FC, useMemo } from 'react';

import {
  useActiveDataLayerLoader,
  useMetaLoader,
} from 'features/files/by-machinery/dataLoaders';
import {
  ApiDataFieldLayer,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';
import { formatMeta } from 'features/files/by-machinery/pages/MatchingAttributesPage/utils/formatMeta';

import { useDelayedState } from 'utils/optimization/useDelayedState';
import { mergeStatuses } from 'modules/entities/utils/mergeStatuses';
import { StatusType } from 'modules/entities/types';

export type DataLoaderProps = {
  component: FC<{
    dataLayers: ApiDataFieldLayer[];
    meta: TasksByMachineryMeta;
  }>;
  loading: FC;
};

export const DataLoader: FC<DataLoaderProps> = ({ component, loading }) => {
  const [dataLayers, requestDataLayer] = useActiveDataLayerLoader();
  const [meta, requestMeta] = useMetaLoader();

  const dataLayersWithoutAdapt = useMemo(
    () => dataLayers.filter(v => v.source.driver !== 'ADAPT'),
    [dataLayers],
  );

  const status = mergeStatuses(requestDataLayer, requestMeta);
  const isReady = useDelayedState(
    status === StatusType.updating || status === StatusType.resolved,
    800,
  );
  if (!isReady || !meta) {
    const LoadingComponent = loading;

    return <LoadingComponent />;
  }

  const Component = component;

  return (
    <Component dataLayers={dataLayersWithoutAdapt} meta={formatMeta(meta)} />
  );
};
