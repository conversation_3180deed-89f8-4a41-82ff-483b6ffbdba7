import {
  all,
  call,
  CallEffect,
  ForkEffect,
  put,
  PutEffect,
  select,
  SelectEffect,
  takeEvery,
} from 'redux-saga/effects';

import * as H from 'history';

// @ts-ignore
import { CropSource } from 'constants/crops';

import bulkUploadModule from 'features/fields/BulkUpload/redux';

import {
  BatchResponse,
  FieldFromFile,
  FieldsUsersFilesFeature,
  LocationState,
} from 'features/fields/BulkUpload/types';

// @ts-ignore
import fields from 'modules/fields';
// @ts-ignore
import map from 'modules/map';
// @ts-ignore
import seasons from 'modules/seasons';

// @ts-ignore
import { logClevertapEvent } from 'sagas/global/integrations';
import logEvent from 'sagas/global/logEvent';
// @ts-ignore
import { zoomToField } from 'sagas/local/add-fields';

import { SeasonType } from 'types/rootState';
import { CropType } from 'types/crops';

import { FIELDS_CREATED_KEY } from 'constants/yield';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
// @ts-ignore
import { apiCall } from 'utils/effects';
// @ts-ignore
import { sendFirstFieldsEvents } from 'utils/fields-analytics';
// @ts-ignore
import { getFieldAreaAsHa } from 'utils/get-field-area';
import { parseQuery } from 'utils/query';

import { getMessageForErrors } from './toasts/getMessageForErrors';

interface SagaHost {
  props: {
    history: H.History<LocationState>;
    onSetPending: (pending: boolean) => void;
  };
}

export default function* saveFieldsSaga(
  host: SagaHost,
): Generator<SelectEffect | ForkEffect | PutEffect, void, number[]> {
  const { history, onSetPending } = host.props;
  const {
    location: { state },
  } = history;

  const fileIds: number[] = yield select(
    bulkUploadModule.selectors.getUploadedFilesIds,
  );

  try {
    const bbox = yield select(bulkUploadModule.selectors.getBbox);

    if (bbox) {
      yield put(
        map.actions.addIntent('fit-bounds', {
          bounds: [bbox.slice(0, 2).reverse(), bbox.slice(2, 4).reverse()],
          mapID: 'imported-fields',
        }),
      );
    }
  } catch (error) {
    // just do nothing
  }

  yield takeEvery(
    fields.actions.createFields.toString(),
    function* (action: {
      payload: { checkedIDs: number[]; allFields: FieldsUsersFilesFeature[] };
    }) {
      const baseUrl = getRootScoutingUrl(history.location.pathname);
      const { checkedIDs, allFields } = action.payload;

      const currentSeason: SeasonType = yield select(
        seasons.selectors.getCurrent,
      );
      const seasonId = currentSeason.id;
      const features = allFields.filter(f => checkedIDs.some(c => c === f.id));

      try {
        onSetPending(true);
        // NOTE: Extract force_folder from URL query
        const query = parseQuery(history.location);
        const folderUuid = query.force_folder
          ? String(query.force_folder)
          : undefined;

        const uploadedFieldsBatchResponse: BatchResponse<FieldFromFile[]> =
          yield call(
            apiCall,
            bulkUploadModule.actions.createFieldsFromFiles(
              seasonId,
              features,
              folderUuid,
            ),
          );

        for (const fileId of fileIds) {
          yield call(logEvent, 'fields_imported_from_file', {
            fileId,
            fields: features,
          });
        }

        // Don't wait to re-fetch anything before redirecting to external app
        if (query.next) {
          const url = new URL(decodeURIComponent(query.next.toString()));
          url.searchParams.set(FIELDS_CREATED_KEY, 'true');
          // @ts-ignore
          window.location = url.toString();
          return;
        }

        const { rows } = yield call(
          apiCall,
          fields.actions.fetchAllOwn(seasonId),
        );

        const uploadedFields = uploadedFieldsBatchResponse.responses.reduce<
          FieldFromFile[]
        >((acc, item) => {
          for (const field of item.response.data) {
            acc.push(field);
          }
          return acc;
        }, []);

        const fieldId = `o${uploadedFields[uploadedFields.length - 1]!.id}`;
        // @ts-ignore
        const field = yield select(fields.selectors.getByID, fieldId);

        sendFirstFieldsEvents(uploadedFields);

        logClevertapEvent('field_new', {
          field_action_type: 'upload',
          field_new_count: uploadedFields.length,
          season: seasonId,
          field_size: uploadedFields.reduce<number>(
            (sum, f) =>
              sum +
              getFieldAreaAsHa(rows.find((r: { id: number }) => r.id === f.id)),
            0,
          ),
          field_user_season_id: field.fieldUserSeasonId,
          field_name: field.title,
        });

        const allCrops = uploadedFields.reduce<CropType[]>(
          (filtered, field) => {
            if (field.crops) {
              filtered.push(...field.crops);
            }
            return filtered;
          },
          [],
        );

        if (allCrops.length > 0) {
          logClevertapEvent('crop_new', {
            source: CropSource.upload,
            crop_count: allCrops.length,
            season: seasonId,
          });
        }

        let nextActions: (PutEffect | CallEffect)[] = [
          put(bulkUploadModule.actions.resetBulkUploadState()),
        ];

        if (state?.from) {
          nextActions.push(call([history, history.replace], state.from));
        } else {
          nextActions.push(call(zoomToField, field));
          nextActions.push(
            call([history, history.replace], `${baseUrl}/${fieldId}`),
          );
        }

        yield all(nextActions);
      } catch (error) {
        onSetPending(false);

        const message = getMessageForErrors({
          titleKey: 'fields.bulk_upload.toasts.saving.submit_failed.title',
          contentKey: 'fields.bulk_upload.toasts.saving.try_again',
          ttl: 5000,
          showOnBulkUploadPage: true,
        });

        if (message) {
          yield put(message);
        }
      }
    },
  );
}
