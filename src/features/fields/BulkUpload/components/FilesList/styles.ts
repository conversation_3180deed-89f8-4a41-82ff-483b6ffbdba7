import { styled } from 'linaria/react';
import { css } from 'linaria';

export const FileListItem = styled.div`
  display: flex;
  flex-flow: row nowrap;
  padding: 11px 0 11px 0;
`;

export const FileListItemContent = styled.div`
  display: flex;
  flex-flow: row nowrap;
  margin-right: auto;
  margin-left: 9px;
`;

export const FileListItemIconWrapper = styled.div`
  margin-right: 17px;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
`;

export const FileListItemDescription = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #5e5e5e;
`;

export const fileName = css`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
`;
