import React, { Component } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { GeoJSON, Pane, Polygon } from 'react-leaflet';
import { DomEvent } from 'leaflet';

import Saga from 'components/saga/Saga';
import MapNavigationLock from 'features/scouting/MapNavigationLock';
import SupportedCountriesLayer from 'features/fields/SupportedCountriesLayer';

import map from 'modules/map';
import fields from 'modules/fields';

import fieldFetcherSaga, { MinZoomToFetch } from 'sagas/local/field-fetcher';
import ZIndex from 'constants/zindex';
import randomID from 'utils/random-id';

const PredictedFieldStyle = {
  color: '#CEC8B6',
  fillOpacity: 0,
  weight: 2,
};

const SelectedStyle = {
  color: '#FFE767',
  fillOpacity: 0.25,
  weight: 2,
};

const DebugCachePages = !!localStorage.getItem('OneSoilDebugCache');

class Field extends Component {
  shouldComponentUpdate() {
    return Boolean(this.props.dynamic);
  }
  render() {
    const { field, style, onClick } = this.props;
    return (
      <Polygon
        key={field.id}
        positions={field.coords}
        onClick={onClick}
        {...style}
      />
    );
  }
}

const PredictedFieldsLayer = () => {
  const zoom = useSelector(map.selectors.getZoomLevel);
  const selectedFieldIDs = useSelector(state =>
    fields.selectors.getSelectedIDs(state),
  );
  const cachePages = useSelector(
    DebugCachePages ? fields.selectors.getCachePages : () => null,
  );
  const predictedFields = useSelector(fields.selectors.getPredicted);
  const data = useSelector(state => fields.selectors.getData(state));

  const dispatch = useDispatch();

  return (
    <>
      <Saga saga={fieldFetcherSaga} />
      <MapNavigationLock />
      <Pane
        className='fields-pane-fields'
        style={{ zIndex: ZIndex.PredictedFieldsPane }}
      >
        {zoom >= MinZoomToFetch &&
          predictedFields.map(field => (
            <Field
              key={field.id}
              field={field}
              style={PredictedFieldStyle}
              onClick={mapEvent => {
                DomEvent.stopPropagation(mapEvent);
                const idCopy = randomID();
                dispatch(
                  fields.actions.setLocal(idCopy, {
                    ...field,
                  }),
                );
                dispatch(fields.actions.addSelectedId(idCopy));
              }}
            />
          ))}
        {DebugCachePages &&
          cachePages.map((page, index) => (
            <GeoJSON
              key={Math.random()}
              data={page.geojson}
              interactive={false}
              style={{
                color: `hsl(${(360 / cachePages.length) * index}, 80%, 50%)`,
              }}
            />
          ))}
      </Pane>
      <Pane
        className='fields-pane-fields'
        style={{ zIndex: ZIndex.SelectionPane }}
      >
        {selectedFieldIDs.map((fieldId, index) => (
          <Field
            dynamic
            key={fieldId}
            field={data[fieldId]}
            style={SelectedStyle}
            onClick={mapEvent => {
              DomEvent.stopPropagation(mapEvent);
              dispatch(fields.actions.removeSelectedId(fieldId));
            }}
          />
        ))}
      </Pane>
      <SupportedCountriesLayer />
    </>
  );
};

export default PredictedFieldsLayer;
